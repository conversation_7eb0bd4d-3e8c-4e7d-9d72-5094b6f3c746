const fs = require('fs')
const path = require('path')
const cp = require('child_process')

async function main() {

    const files = fs.readdirSync(__dirname)
        .filter(name => !name.endsWith('.js'))
        .filter(name => !fs.lstatSync(path.join(__dirname, name)).isDirectory())

    if (files.length === 0) {
        console.log('nothing to transpile')
        return
    }

    files.forEach(file => {
        console.log('Transpile:', file)
        transpile(file);
        console.log('Done:', file, "\n")
    })

}

main()

function transpile(file) {
    const ffmpeg = cp.spawnSync('ffmpeg', [
        "-hwaccel",
        "cuda",
        "-i",
        `.\\${file}`,
        "-map",
        "0",
        "-c:v",
        "copy",
        "-c:s",
        "copy",
        "-c:a",
        "ac3",
        "-b:a",
        "640k",
        `.\\done\\${file}`,
    ], {
        cwd: process.cwd(),
        env: process.env,
        stdio: 'pipe',
        encoding: 'utf-8'
    });
    console.log(ffmpeg.output)
};