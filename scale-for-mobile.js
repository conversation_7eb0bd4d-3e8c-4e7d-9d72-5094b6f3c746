const fs = require('fs')
const path = require('path')
const cp = require('child_process')

// Configuration - set your target path here
const TARGET_PATH = './scaledown' // Change this to your desired path

async function main() {
    console.log(`Starting mobile scaling process for path: ${TARGET_PATH}`)

    if (!fs.existsSync(TARGET_PATH)) {
        console.error(`Error: Target path "${TARGET_PATH}" does not exist`)
        return
    }

    const mkvFiles = findMkvFiles(TARGET_PATH)

    if (mkvFiles.length === 0) {
        console.log('No .mkv files found to process')
        return
    }

    console.log(`Found ${mkvFiles.length} .mkv file(s) to process:`)
    mkvFiles.forEach(file => console.log(`  - ${file}`))
    console.log('')

    for (const file of mkvFiles) {
        try {
            console.log(`Processing: ${file}`)
            await scaleForMobile(file)
            console.log(`Completed: ${file}\n`)
        } catch (error) {
            console.error(`Error processing ${file}:`, error.message)
            console.log('')
        }
    }

    console.log('Mobile scaling process completed!')
}

function findMkvFiles(dirPath) {
    const mkvFiles = []

    function traverseDirectory(currentPath) {
        try {
            const items = fs.readdirSync(currentPath)

            for (const item of items) {
                const fullPath = path.join(currentPath, item)
                const stats = fs.lstatSync(fullPath)

                if (stats.isDirectory()) {
                    // Recursively traverse subdirectories
                    traverseDirectory(fullPath)
                } else if (stats.isFile() && path.extname(item).toLowerCase() === '.mkv') {
                    // Check if mobile version already exists
                    const dir = path.dirname(fullPath)
                    const name = path.basename(fullPath, '.mkv')
                    const mobileVersion = path.join(dir, `${name}-[480p].mkv`)

                    if (!fs.existsSync(mobileVersion)) {
                        mkvFiles.push(fullPath)
                    } else {
                        console.log(`Skipping ${fullPath} - mobile version already exists`)
                    }
                }
            }
        } catch (error) {
            console.error(`Error reading directory ${currentPath}:`, error.message)
        }
    }

    traverseDirectory(dirPath)
    return mkvFiles
}

function scaleForMobile(inputFile) {
    return new Promise((resolve, reject) => {
        const dir = path.dirname(inputFile)
        const name = path.basename(inputFile, '.mkv')
        const outputFile = path.join(dir, `${name}-[480p].mkv`)

        console.log(`  Input: ${inputFile}`)
        console.log(`  Output: ${outputFile}`)

        const ffmpegArgs = [
            "-hwaccel", "cuda",
            "-i", inputFile,
            "-vf", "scale=-2:480",  // Scale to 480p height, maintain aspect ratio
            "-c:v", "libx264",      // Use H.264 codec for better mobile compatibility
            "-preset", "fast",      // Faster encoding
            "-crf", "23",           // Good quality/size balance
            "-c:a", "copy",         // Copy audio without re-encoding
            "-c:s", "copy",         // Copy subtitles
            "-movflags", "+faststart", // Optimize for streaming
            outputFile
        ]

        console.log(`  Running: ffmpeg ${ffmpegArgs.join(' ')}`)

        const ffmpeg = cp.spawn('ffmpeg', ffmpegArgs, {
            cwd: process.cwd(),
            env: process.env,
            stdio: ['pipe', 'pipe', 'pipe']
        })

        let stdout = ''
        let stderr = ''

        ffmpeg.stdout.on('data', (data) => {
            stdout += data.toString()
        })

        ffmpeg.stderr.on('data', (data) => {
            stderr += data.toString()
        })

        ffmpeg.on('close', (code) => {
            if (code === 0) {
                console.log(`  ✓ Successfully created mobile version`)
                resolve()
            } else {
                console.error(`  ✗ FFmpeg failed with exit code ${code}`)
                if (stderr) {
                    console.error(`  Error output: ${stderr.slice(-500)}`) // Show last 500 chars
                }
                reject(new Error(`FFmpeg process failed with exit code ${code}`))
            }
        })

        ffmpeg.on('error', (error) => {
            console.error(`  ✗ Failed to start FFmpeg process:`, error.message)
            reject(error)
        })
    })
}

main().catch(error => {
    console.error('Fatal error:', error.message)
    process.exit(1)
})
