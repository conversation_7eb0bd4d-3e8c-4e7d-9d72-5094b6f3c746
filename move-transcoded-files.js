const fs = require('fs')
const path = require('path')

// Configuration - set your paths here
const TARGET_PATH = 'Y:\\Kinder-Serien\\Peppa Wutz' // Source directory to search for transcoded files
const SAVE_PATH = 'Y:\\Kinder-Serien-Mobile\\Peppa Wutz' // Destination directory to move files to

async function main() {
    console.log(`Starting move process for transcoded files`)
    console.log(`Source path: ${TARGET_PATH}`)
    console.log(`Destination path: ${SAVE_PATH}`)

    if (!fs.existsSync(TARGET_PATH)) {
        console.error(`Error: Target path "${TARGET_PATH}" does not exist`)
        return
    }

    if (!SAVE_PATH) {
        console.error(`Error: SAVE_PATH must be defined`)
        return
    }

    // Create save path if it doesn't exist
    try {
        fs.mkdirSync(SAVE_PATH, { recursive: true })
        console.log(`Destination directory ready: ${SAVE_PATH}`)
    } catch (error) {
        console.error(`Error: Could not create save path "${SAVE_PATH}":`, error.message)
        return
    }

    const transcodedFiles = findTranscodedFiles(TARGET_PATH)

    if (transcodedFiles.length === 0) {
        console.log('No transcoded files (ending with [480p]) found to move')
        return
    }

    console.log(`\nFound ${transcodedFiles.length} transcoded file(s) to move:`)
    transcodedFiles.forEach(file => console.log(`  - ${file}`))
    console.log('')

    let movedCount = 0
    let errorCount = 0

    for (const file of transcodedFiles) {
        try {
            console.log(`Moving: ${file}`)
            await moveTranscodedFile(file)
            movedCount++
            console.log(`  ✓ Successfully moved\n`)
        } catch (error) {
            errorCount++
            console.error(`  ✗ Error moving ${file}:`, error.message)
            console.log('')
        }
    }

    console.log(`Move process completed!`)
    console.log(`Successfully moved: ${movedCount} files`)
    if (errorCount > 0) {
        console.log(`Errors encountered: ${errorCount} files`)
    }
}

function findTranscodedFiles(dirPath) {
    const transcodedFiles = []

    function traverseDirectory(currentPath) {
        try {
            const items = fs.readdirSync(currentPath)

            for (const item of items) {
                const fullPath = path.join(currentPath, item)
                const stats = fs.lstatSync(fullPath)

                if (stats.isDirectory()) {
                    // Recursively traverse subdirectories
                    traverseDirectory(fullPath)
                } else if (stats.isFile() && item.includes('[480p]')) {
                    // Found a transcoded file
                    transcodedFiles.push(fullPath)
                }
            }
        } catch (error) {
            console.error(`Error reading directory ${currentPath}:`, error.message)
        }
    }

    traverseDirectory(dirPath)
    return transcodedFiles
}

function moveTranscodedFile(inputFile) {
    return new Promise((resolve, reject) => {
        try {
            // Calculate relative path from TARGET_PATH and replicate in SAVE_PATH
            const relativePath = path.relative(TARGET_PATH, inputFile)
            const relativeDir = path.dirname(relativePath)
            const fileName = path.basename(inputFile)
            const targetDir = path.join(SAVE_PATH, relativeDir)
            const outputFile = path.join(targetDir, fileName)

            console.log(`  From: ${inputFile}`)
            console.log(`  To: ${outputFile}`)

            // Create target directory if it doesn't exist
            try {
                fs.mkdirSync(targetDir, { recursive: true })
            } catch (error) {
                console.error(`  ✗ Failed to create directory ${targetDir}:`, error.message)
                reject(error)
                return
            }

            // Check if destination file already exists
            if (fs.existsSync(outputFile)) {
                console.log(`  ! Destination file already exists, skipping`)
                resolve()
                return
            }

            // Move the file (rename operation)
            fs.rename(inputFile, outputFile, (error) => {
                if (error) {
                    console.error(`  ✗ Failed to move file:`, error.message)
                    reject(error)
                } else {
                    resolve()
                }
            })

        } catch (error) {
            console.error(`  ✗ Error processing file:`, error.message)
            reject(error)
        }
    })
}

main().catch(error => {
    console.error('Fatal error:', error.message)
    process.exit(1)
})
